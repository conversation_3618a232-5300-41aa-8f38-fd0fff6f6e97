/* Custom CSS for Arabic Tools Website */

/* Root Variables */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --card-bg: #ffffff;
    --hover-bg: #f3f4f6;
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-accent: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius: 1rem;
    --border-radius-sm: 0.5rem;
    --border-radius-lg: 1.5rem;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.7;
    color: var(--text-primary);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

.display-1, .display-2, .display-3, .display-4, .display-5 {
    font-weight: 700;
}

/* Innovative Navigation */
.innovative-nav {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    padding: 1rem 0;
    z-index: 1000;
}

.innovative-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

/* Brand Section */
.nav-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1001;
}

.brand-circle {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.brand-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.brand-circle:hover::before {
    left: 100%;
}

.brand-circle:hover {
    transform: scale(1.1) rotate(360deg);
    box-shadow: var(--shadow-xl);
}

.brand-circle i {
    font-size: 1.5rem;
    color: white;
    z-index: 2;
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Floating Menu */
.floating-menu {
    position: relative;
    z-index: 1001;
}

.menu-toggle {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.menu-toggle:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-xl);
}

.menu-toggle.active {
    background: var(--gradient-accent);
    transform: scale(0.9);
}

/* Hamburger Animation */
.hamburger {
    width: 24px;
    height: 18px;
    position: relative;
    transform: rotate(0deg);
    transition: 0.5s ease-in-out;
}

.hamburger span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: white;
    border-radius: 2px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: 0.25s ease-in-out;
}

.hamburger span:nth-child(1) {
    top: 0px;
}

.hamburger span:nth-child(2) {
    top: 7px;
}

.hamburger span:nth-child(3) {
    top: 14px;
}

.menu-toggle.active .hamburger span:nth-child(1) {
    top: 7px;
    transform: rotate(135deg);
}

.menu-toggle.active .hamburger span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.menu-toggle.active .hamburger span:nth-child(3) {
    top: 7px;
    transform: rotate(-135deg);
}

/* Radial Menu */
.radial-menu {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    pointer-events: none;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.radial-menu.active {
    opacity: 1;
    pointer-events: all;
}

.menu-item {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    margin: -30px 0 0 -30px;
    transform-origin: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.menu-item .menu-icon {
    width: 60px;
    height: 60px;
    background: var(--card-bg);
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.menu-item .menu-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.menu-item:hover .menu-icon::before {
    opacity: 1;
}

.menu-item .menu-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.menu-item:hover .menu-icon i {
    color: white;
    transform: scale(1.2);
}

.menu-item .menu-label {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--card-bg);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    box-shadow: var(--shadow-md);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.menu-item:hover .menu-label {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* Position menu items in circle */
.radial-menu.active .menu-item[data-angle="0"] { transform: translate(120px, -30px); }
.radial-menu.active .menu-item[data-angle="45"] { transform: translate(85px, -115px); }
.radial-menu.active .menu-item[data-angle="90"] { transform: translate(0px, -150px); }
.radial-menu.active .menu-item[data-angle="135"] { transform: translate(-115px, -115px); }
.radial-menu.active .menu-item[data-angle="180"] { transform: translate(-150px, -30px); }
.radial-menu.active .menu-item[data-angle="225"] { transform: translate(-115px, 55px); }
.radial-menu.active .menu-item[data-angle="270"] { transform: translate(0px, 90px); }
.radial-menu.active .menu-item[data-angle="315"] { transform: translate(85px, 55px); }

/* Quick Access Dock */
.quick-dock {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
    border-radius: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.quick-dock:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.dock-item {
    width: 45px;
    height: 45px;
    background: var(--card-bg);
    border: 2px solid transparent;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.dock-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.dock-item:hover::before {
    opacity: 1;
}

.dock-item:hover {
    transform: translateY(-5px) scale(1.1);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.dock-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.dock-item:hover i {
    color: white;
    transform: scale(1.1);
}

.dock-item.home-btn {
    background: var(--gradient-accent);
    border-color: var(--accent-color);
}

.dock-item.home-btn i {
    color: white;
}

.dock-item.home-btn:hover {
    background: var(--gradient-primary);
}

.dock-separator {
    width: 2px;
    height: 30px;
    background: var(--border-color);
    border-radius: 1px;
    margin: 0 0.25rem;
}

/* Dock Tooltip */
.dock-tooltip {
    position: fixed;
    background: var(--dark-color);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 1002;
    box-shadow: var(--shadow-lg);
}

.dock-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--dark-color);
}

/* Menu Overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 999;
}

.menu-overlay.active {
    opacity: 1;
    pointer-events: all;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Navigation Enhancements */
.brand-circle,
.menu-toggle,
.menu-item .menu-icon,
.dock-item {
    position: relative;
    overflow: hidden;
}

/* Smooth transitions for all interactive elements */
.innovative-nav * {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for accessibility */
.menu-toggle:focus,
.dock-item:focus,
.brand-circle:focus,
.menu-item:focus {
    outline: 3px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

.menu-item.focused .menu-icon {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.menu-item.focused .menu-label {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* Keyboard shortcuts hint */
.shortcuts-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--dark-color);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    max-width: 250px;
}

.shortcuts-hint.show {
    opacity: 1;
    transform: translateY(0);
}

.shortcuts-hint h6 {
    margin-bottom: 0.5rem;
    color: var(--accent-color);
}

.shortcuts-hint ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shortcuts-hint li {
    margin-bottom: 0.25rem;
    display: flex;
    justify-content: space-between;
}

.shortcuts-hint kbd {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
}

/* Loading state for menu items */
.menu-item.loading .menu-icon {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 2rem 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-section .lead {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.hero-section .btn {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 700;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.hero-image {
    text-align: center;
    animation: float 4s ease-in-out infinite;
}

.hero-image i {
    font-size: 8rem;
    opacity: 0.8;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(2deg); }
    50% { transform: translateY(-25px) rotate(0deg); }
    75% { transform: translateY(-15px) rotate(-2deg); }
}

/* Tool Cards */
.tool-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    overflow: hidden;
    position: relative;
    padding: 2rem 1.5rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tool-card:hover::before {
    opacity: 1;
}

.tool-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.tool-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
}

.tool-icon::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-accent);
    border-radius: var(--border-radius);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tool-card:hover .tool-icon::after {
    opacity: 1;
}

.tool-icon i {
    font-size: 2.25rem;
    color: white;
    transition: all 0.3s ease;
}

.tool-card:hover .tool-icon {
    transform: scale(1.1) rotate(5deg);
}

.tool-card .card-body {
    padding: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.tool-card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.tool-card .card-text {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Tools Section */
#tools {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    padding: 5rem 0;
}

#tools h2 {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
}

#tools h2::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

#tools .lead {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    font-weight: 500;
}

/* Tool Content Area */
.tool-content {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 3rem;
    margin: 3rem 0;
    border: 1px solid var(--border-color);
}

.tool-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 3px solid var(--border-color);
    position: relative;
}

.tool-header::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.tool-header h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.tool-header h2 i {
    color: var(--primary-color);
    font-size: 2rem;
}

.tool-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0;
    line-height: 1.6;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-color);
    padding: 1rem 1.25rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--card-bg);
    color: var(--text-primary);
    font-family: 'Cairo', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.15);
    background: var(--card-bg);
    color: var(--text-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.8;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.btn {
    border-radius: var(--border-radius);
    padding: 1rem 2rem;
    font-weight: 700;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-secondary {
    border: 2px solid var(--text-secondary);
    color: var(--text-secondary);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--text-secondary);
    color: white;
    transform: translateY(-2px);
}

.btn-success {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn i {
    font-size: 1.1rem;
}

/* Result Display */
.result-display {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-top: 2rem;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.result-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--gradient-primary);
}

.result-display.success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
    border-color: rgba(16, 185, 129, 0.3);
}

.result-display.success::before {
    background: var(--success-color);
}

.result-display.error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
    border-color: rgba(239, 68, 68, 0.3);
}

.result-display.error::before {
    background: var(--danger-color);
}

.result-display.warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
    border-color: rgba(245, 158, 11, 0.3);
}

.result-display.warning::before {
    background: var(--warning-color);
}

.result-display h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
}

.result-display p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.result-display .bg-light {
    background: var(--hover-bg) !important;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.result-display .bg-light:hover {
    background: var(--card-bg) !important;
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-section h1 {
        font-size: 3rem;
    }

    .hero-image i {
        font-size: 6rem;
    }

    #tools h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 992px) {
    .hero-section {
        text-align: center;
        padding: 3rem 0;
    }

    .hero-section h1 {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }

    .hero-section .lead {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .hero-image i {
        font-size: 5rem;
    }

    .tool-content {
        padding: 2rem;
    }

    .tool-header h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .innovative-nav {
        padding: 0.75rem 0;
    }

    .brand-text {
        font-size: 1.25rem;
    }

    .brand-circle {
        width: 45px;
        height: 45px;
    }

    .brand-circle i {
        font-size: 1.25rem;
    }

    .menu-toggle {
        width: 55px;
        height: 55px;
    }

    .quick-dock {
        display: none;
    }

    .radial-menu {
        width: 250px;
        height: 250px;
    }

    .menu-item .menu-icon {
        width: 50px;
        height: 50px;
    }

    .menu-item .menu-icon i {
        font-size: 1.25rem;
    }

    .menu-item .menu-label {
        font-size: 0.75rem;
        top: 60px;
    }

    /* Adjust radial positions for mobile */
    .radial-menu.active .menu-item[data-angle="0"] { transform: translate(100px, -25px); }
    .radial-menu.active .menu-item[data-angle="45"] { transform: translate(70px, -95px); }
    .radial-menu.active .menu-item[data-angle="90"] { transform: translate(0px, -125px); }
    .radial-menu.active .menu-item[data-angle="135"] { transform: translate(-95px, -95px); }
    .radial-menu.active .menu-item[data-angle="180"] { transform: translate(-125px, -25px); }
    .radial-menu.active .menu-item[data-angle="225"] { transform: translate(-95px, 45px); }
    .radial-menu.active .menu-item[data-angle="270"] { transform: translate(0px, 75px); }
    .radial-menu.active .menu-item[data-angle="315"] { transform: translate(70px, 45px); }

    .hero-section {
        padding: 2rem 0;
        min-height: 80vh;
    }

    .hero-section h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .hero-section .lead {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-image i {
        font-size: 4rem;
    }

    #tools {
        padding: 3rem 0;
    }

    #tools h2 {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    #tools .lead {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .tool-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem 1rem;
    }

    .tool-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1rem;
    }

    .tool-icon i {
        font-size: 1.75rem;
    }

    .tool-card .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .tool-card .card-text {
        font-size: 0.9rem;
    }

    .tool-content {
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .tool-header {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
    }

    .tool-header h2 {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .tool-header h2 i {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }

    .navbar-nav {
        text-align: center;
        padding-top: 1rem;
    }

    .dropdown-menu {
        text-align: center;
        margin-top: 0.5rem;
    }

    .result-display {
        padding: 1.5rem;
        margin-top: 1.5rem;
    }
}

@media (max-width: 576px) {
    .innovative-nav {
        padding: 0.5rem 0;
    }

    .brand-text {
        font-size: 1.1rem;
    }

    .brand-circle {
        width: 40px;
        height: 40px;
    }

    .brand-circle i {
        font-size: 1.1rem;
    }

    .menu-toggle {
        width: 50px;
        height: 50px;
    }

    .hamburger {
        width: 20px;
        height: 15px;
    }

    .hamburger span {
        height: 2px;
    }

    .radial-menu {
        width: 200px;
        height: 200px;
    }

    .menu-item .menu-icon {
        width: 45px;
        height: 45px;
    }

    .menu-item .menu-icon i {
        font-size: 1.1rem;
    }

    .menu-item .menu-label {
        font-size: 0.7rem;
        top: 55px;
        padding: 0.25rem 0.75rem;
    }

    /* Tighter radial positions for small screens */
    .radial-menu.active .menu-item[data-angle="0"] { transform: translate(80px, -22px); }
    .radial-menu.active .menu-item[data-angle="45"] { transform: translate(56px, -78px); }
    .radial-menu.active .menu-item[data-angle="90"] { transform: translate(0px, -100px); }
    .radial-menu.active .menu-item[data-angle="135"] { transform: translate(-78px, -78px); }
    .radial-menu.active .menu-item[data-angle="180"] { transform: translate(-100px, -22px); }
    .radial-menu.active .menu-item[data-angle="225"] { transform: translate(-78px, 34px); }
    .radial-menu.active .menu-item[data-angle="270"] { transform: translate(0px, 56px); }
    .radial-menu.active .menu-item[data-angle="315"] { transform: translate(56px, 34px); }

    .hero-section h1 {
        font-size: 1.75rem;
    }

    .hero-section .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .tool-card {
        padding: 1.25rem 0.75rem;
    }

    .tool-icon {
        width: 60px;
        height: 60px;
    }

    .tool-icon i {
        font-size: 1.5rem;
    }

    .tool-content {
        padding: 1.25rem;
    }

    .tool-header h2 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
    }
}

/* Advertisement Styles */
.ad-container {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 2px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.ad-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-secondary);
}

.ad-container ins {
    display: block;
    min-height: 120px;
    background: linear-gradient(135deg, var(--hover-bg) 0%, var(--light-color) 100%);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.sidebar-ad {
    position: sticky;
    top: 120px;
    margin-bottom: 2rem;
}

.tool-ad {
    margin: 3rem 0;
    text-align: center;
}

.ad-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
    font-weight: 600;
    text-align: center;
}

.ad-label::before,
.ad-label::after {
    content: '';
    display: inline-block;
    width: 30px;
    height: 1px;
    background: var(--border-color);
    vertical-align: middle;
    margin: 0 10px;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.bg-gradient-accent {
    background: var(--gradient-accent);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.shadow-hover {
    transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
    box-shadow: var(--shadow-xl);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

.text-primary-custom {
    color: var(--text-primary) !important;
}

.text-secondary-custom {
    color: var(--text-secondary) !important;
}

.text-muted-custom {
    color: var(--text-muted) !important;
}

/* Interactive Elements */
.interactive {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.interactive:hover {
    transform: translateY(-2px);
}

.interactive:active {
    transform: translateY(0);
}

/* Focus States */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for tool cards */
.tool-card:nth-child(1) { animation-delay: 0.1s; }
.tool-card:nth-child(2) { animation-delay: 0.2s; }
.tool-card:nth-child(3) { animation-delay: 0.3s; }
.tool-card:nth-child(4) { animation-delay: 0.4s; }
.tool-card:nth-child(5) { animation-delay: 0.5s; }
.tool-card:nth-child(6) { animation-delay: 0.6s; }
.tool-card:nth-child(7) { animation-delay: 0.7s; }
.tool-card:nth-child(8) { animation-delay: 0.8s; }

/* Stagger animation for menu items */
.radial-menu.active .menu-item:nth-child(1) { animation-delay: 0.1s; }
.radial-menu.active .menu-item:nth-child(2) { animation-delay: 0.15s; }
.radial-menu.active .menu-item:nth-child(3) { animation-delay: 0.2s; }
.radial-menu.active .menu-item:nth-child(4) { animation-delay: 0.25s; }
.radial-menu.active .menu-item:nth-child(5) { animation-delay: 0.3s; }
.radial-menu.active .menu-item:nth-child(6) { animation-delay: 0.35s; }
.radial-menu.active .menu-item:nth-child(7) { animation-delay: 0.4s; }
.radial-menu.active .menu-item:nth-child(8) { animation-delay: 0.45s; }

.radial-menu.active .menu-item {
    animation: menuItemAppear 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes menuItemAppear {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Scroll reveal animation */
@keyframes scrollReveal {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.scroll-reveal {
    animation: scrollReveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Footer */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, var(--dark-color) 0%, #0f172a 100%) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
}

footer h5 {
    color: white;
    font-weight: 700;
    margin-bottom: 1rem;
}

footer p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

footer .text-muted {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Tic Tac Toe Styles */
.tic-tac-toe-board {
    width: 300px;
    height: 300px;
}

.tic-tac-toe-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
    padding: 2px;
}

.tic-tac-toe-cell {
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: calc(var(--border-radius) - 2px);
    min-height: 96px;
}

.tic-tac-toe-cell:hover {
    background-color: var(--light-color);
    transform: scale(0.95);
}

.tic-tac-toe-cell.player-x {
    color: var(--primary-color);
}

.tic-tac-toe-cell.player-o {
    color: var(--success-color);
}

.tic-tac-toe-cell.winning-cell {
    background-color: var(--accent-color);
    color: white;
    animation: pulse 1s infinite;
}

.tic-tac-toe-cell.disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Text on Image Styles */
#imageCanvas {
    cursor: crosshair;
    max-width: 100%;
    height: auto;
}

.text-element-selected {
    border: 2px dashed var(--primary-color);
}

/* Word Counter Styles */
#textToAnalyze {
    font-family: 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    resize: vertical;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.stat-card h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Responsive Tic Tac Toe */
@media (max-width: 576px) {
    .tic-tac-toe-board {
        width: 250px;
        height: 250px;
    }

    .tic-tac-toe-cell {
        font-size: 1.5rem;
        min-height: 80px;
    }
}

/* Print Styles */
@media print {
    .navbar, footer, .btn {
        display: none !important;
    }

    .tool-content {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .tic-tac-toe-board, #imageCanvas {
        break-inside: avoid;
    }
}
