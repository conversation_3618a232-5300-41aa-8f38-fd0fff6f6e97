/* Custom CSS for Arabic Tools Website */

/* Root Variables */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 0.75rem;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f8fafc;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
}

.display-1, .display-2, .display-3, .display-4, .display-5 {
    font-weight: 700;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: calc(var(--border-radius) - 0.25rem);
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-image {
    text-align: center;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Tool Cards */
.tool-card {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.tool-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.tool-icon i {
    font-size: 2rem;
    color: white;
}

.tool-card:hover .tool-icon {
    transform: scale(1.1);
    background: var(--gradient-secondary);
}

/* Tool Content Area */
.tool-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin: 2rem 0;
}

.tool-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-color);
}

.tool-header h2 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Result Display */
.result-display {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
    border-left: 4px solid var(--primary-color);
}

.result-display.success {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: var(--success-color);
}

.result-display.error {
    background: rgba(239, 68, 68, 0.1);
    border-left-color: var(--danger-color);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .tool-card {
        margin-bottom: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .dropdown-menu {
        text-align: center;
    }
}

/* Advertisement Styles */
.ad-container {
    margin: 1rem 0;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e2e8f0;
}

.ad-container ins {
    display: block;
    min-height: 90px;
    background: #f8f9fa;
    border-radius: var(--border-radius);
}

.sidebar-ad {
    position: sticky;
    top: 100px;
    margin-bottom: 2rem;
}

.tool-ad {
    margin: 2rem 0;
    text-align: center;
}

.ad-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-secondary {
    background: var(--gradient-secondary);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Footer */
footer {
    margin-top: auto;
}

/* Tic Tac Toe Styles */
.tic-tac-toe-board {
    width: 300px;
    height: 300px;
}

.tic-tac-toe-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
    padding: 2px;
}

.tic-tac-toe-cell {
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: calc(var(--border-radius) - 2px);
    min-height: 96px;
}

.tic-tac-toe-cell:hover {
    background-color: var(--light-color);
    transform: scale(0.95);
}

.tic-tac-toe-cell.player-x {
    color: var(--primary-color);
}

.tic-tac-toe-cell.player-o {
    color: var(--success-color);
}

.tic-tac-toe-cell.winning-cell {
    background-color: var(--accent-color);
    color: white;
    animation: pulse 1s infinite;
}

.tic-tac-toe-cell.disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Text on Image Styles */
#imageCanvas {
    cursor: crosshair;
    max-width: 100%;
    height: auto;
}

.text-element-selected {
    border: 2px dashed var(--primary-color);
}

/* Word Counter Styles */
#textToAnalyze {
    font-family: 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    resize: vertical;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.stat-card h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Responsive Tic Tac Toe */
@media (max-width: 576px) {
    .tic-tac-toe-board {
        width: 250px;
        height: 250px;
    }

    .tic-tac-toe-cell {
        font-size: 1.5rem;
        min-height: 80px;
    }
}

/* Print Styles */
@media print {
    .navbar, footer, .btn {
        display: none !important;
    }

    .tool-content {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .tic-tac-toe-board, #imageCanvas {
        break-inside: avoid;
    }
}
