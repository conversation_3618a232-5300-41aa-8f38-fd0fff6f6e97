// Main JavaScript file for Arabic Tools Website

// Global variables
let currentTool = null;
const API_KEY = 'your-exchange-api-key'; // Replace with actual API key

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupEventListeners();
    setupSmoothScrolling();
    setupNavbarScroll();
    setupScrollAnimations();
    setupToolCardAnimations();
}

// Setup Event Listeners
function setupEventListeners() {
    // Tool card clicks
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const toolName = this.dataset.tool;
            loadTool(toolName);
        });
    });

    // Navigation links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                if (targetId === 'home') {
                    scrollToTop();
                } else {
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    });
}

// Setup Smooth Scrolling
function setupSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Setup Navbar Scroll Effect
function setupNavbarScroll() {
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.backgroundColor = 'rgba(37, 99, 235, 0.95)';
        } else {
            navbar.style.backgroundColor = 'rgba(37, 99, 235, 1)';
        }
    });
}

// Load Tool Function
function loadTool(toolName) {
    const toolContent = document.getElementById('tool-content');
    toolContent.innerHTML = '';
    toolContent.classList.remove('d-none');
    
    // Scroll to tool content
    toolContent.scrollIntoView({ behavior: 'smooth' });
    
    // Load specific tool
    switch(toolName) {
        case 'currency-converter':
            loadCurrencyConverter();
            break;
        case 'number-to-words':
            loadNumberToWords();
            break;
        case 'unit-converter':
            loadUnitConverter();
            break;
        case 'age-calculator':
            loadAgeCalculator();
            break;
        case 'date-difference':
            loadDateDifference();
            break;
        case 'word-counter':
            loadWordCounter();
            break;
        case 'text-on-image':
            loadTextOnImage();
            break;
        case 'tic-tac-toe':
            loadTicTacToe();
            break;
        default:
            showError('الأداة غير متوفرة حالياً');
    }
    
    currentTool = toolName;
}

// Currency Converter Tool
function loadCurrencyConverter() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h2><i class="fas fa-exchange-alt me-2"></i>تحويل العملات</h2>
                        <button type="button" class="btn btn-outline-secondary" onclick="backToHome()">
                            <i class="fas fa-home me-2"></i>الرئيسية
                        </button>
                    </div>
                    <p class="text-muted">تحويل بين العملات المختلفة بأسعار حقيقية ومحدثة</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="currencyForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">المبلغ</label>
                                    <input type="number" class="form-control" id="amount" placeholder="أدخل المبلغ" min="0" step="0.01" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">من</label>
                                    <select class="form-select" id="fromCurrency" required>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="GBP">جنيه إسترليني (GBP)</option>
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="JOD">دينار أردني (JOD)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">إلى</label>
                                    <select class="form-select" id="toCurrency" required>
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="GBP">جنيه إسترليني (GBP)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="JOD">دينار أردني (JOD)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                    </select>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>تحويل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="swapCurrencies()">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <div id="currencyResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('tool-content').innerHTML = html;
    
    // Setup form submission
    document.getElementById('currencyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        convertCurrency();
    });
}

// Convert Currency Function
async function convertCurrency() {
    const amount = document.getElementById('amount').value;
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    const resultDiv = document.getElementById('currencyResult');
    
    if (!amount || amount <= 0) {
        showResult('يرجى إدخال مبلغ صحيح', 'error', resultDiv);
        return;
    }
    
    if (fromCurrency === toCurrency) {
        showResult(`${amount} ${fromCurrency} = ${amount} ${toCurrency}`, 'success', resultDiv);
        return;
    }
    
    // Show loading
    const loadingOverlay = showLoading(resultDiv);
    showResult('<div class="text-center"><div class="loading-spinner"></div><p class="mt-3 mb-0">جاري التحويل...</p></div>', 'info', resultDiv);
    
    try {
        // Using a free API for currency conversion
        const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${fromCurrency}`);
        const data = await response.json();
        
        if (data.rates && data.rates[toCurrency]) {
            const rate = data.rates[toCurrency];
            const convertedAmount = (amount * rate).toFixed(2);
            
            const resultHTML = `
                <div class="text-center">
                    <h4 class="text-primary">${amount} ${fromCurrency} = ${convertedAmount} ${toCurrency}</h4>
                    <p class="text-muted">سعر الصرف: 1 ${fromCurrency} = ${rate.toFixed(4)} ${toCurrency}</p>
                    <small class="text-muted">آخر تحديث: ${new Date().toLocaleString('ar-SA')}</small>
                </div>
            `;
            
            showResult(resultHTML, 'success', resultDiv);
        } else {
            showResult('عذراً، لا يمكن الحصول على سعر الصرف حالياً', 'error', resultDiv);
        }
    } catch (error) {
        console.error('Currency conversion error:', error);
        showResult('حدث خطأ أثناء التحويل. يرجى المحاولة مرة أخرى', 'error', resultDiv);
    }
}

// Swap Currencies Function
function swapCurrencies() {
    const fromCurrency = document.getElementById('fromCurrency');
    const toCurrency = document.getElementById('toCurrency');
    
    const temp = fromCurrency.value;
    fromCurrency.value = toCurrency.value;
    toCurrency.value = temp;
}

// Number to Words Tool
function loadNumberToWords() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-spell-check me-2"></i>تفقيط الأرقام</h2>
                    <p class="text-muted">تحويل الأرقام إلى كلمات باللغة العربية بطريقة محاسبية صحيحة</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="numberToWordsForm">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الرقم المراد تفقيطه</label>
                                <input type="number" class="form-control" id="numberInput" placeholder="أدخل الرقم (مثال: 1234.56)" step="0.01" required>
                                <div class="form-text">يمكن إدخال الأرقام العشرية أيضاً</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">العملة</label>
                                <select class="form-select" id="currencyType">
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                    <option value="EGP">جنيه مصري</option>
                                    <option value="AED">درهم إماراتي</option>
                                    <option value="JOD">دينار أردني</option>
                                    <option value="KWD">دينار كويتي</option>
                                    <option value="NONE">بدون عملة</option>
                                </select>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic me-2"></i>تفقيط الرقم
                                </button>
                            </div>
                        </form>
                        
                        <div id="numberToWordsResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('tool-content').innerHTML = html;
    
    // Setup form submission
    document.getElementById('numberToWordsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        convertNumberToWords();
    });
}

// Convert Number to Words Function
function convertNumberToWords() {
    const number = parseFloat(document.getElementById('numberInput').value);
    const currency = document.getElementById('currencyType').value;
    const resultDiv = document.getElementById('numberToWordsResult');
    
    if (isNaN(number)) {
        showResult('يرجى إدخال رقم صحيح', 'error', resultDiv);
        return;
    }
    
    if (number < 0) {
        showResult('لا يمكن تفقيط الأرقام السالبة', 'error', resultDiv);
        return;
    }
    
    if (number > 999999999999) {
        showResult('الرقم كبير جداً. الحد الأقصى هو 999,999,999,999', 'error', resultDiv);
        return;
    }
    
    try {
        const words = numberToArabicWords(number, currency);
        const resultHTML = `
            <div class="text-center">
                <h5 class="text-primary mb-3">النتيجة:</h5>
                <div class="alert alert-success">
                    <h4>${words}</h4>
                </div>
                <p class="text-muted">الرقم: ${number.toLocaleString('ar-SA')}</p>
            </div>
        `;
        
        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Number to words error:', error);
        showResult('حدث خطأ أثناء التفقيط', 'error', resultDiv);
    }
}

// Utility Functions
function showResult(content, type = 'info', container = null) {
    showResultEnhanced(content, type, container, true);
}

function showError(message) {
    const toolContent = document.getElementById('tool-content');
    toolContent.innerHTML = `
        <div class="container">
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        </div>
    `;
}

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Unit Converter Tool
function loadUnitConverter() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-ruler me-2"></i>تحويل وحدات القياس</h2>
                    <p class="text-muted">تحويل بين وحدات الطول والوزن والحجم ودرجة الحرارة</p>
                </div>

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="unitConverterForm">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label class="form-label fw-bold">نوع الوحدة</label>
                                    <select class="form-select" id="unitCategory" required>
                                        <option value="">اختر نوع الوحدة</option>
                                        <option value="length">الطول</option>
                                        <option value="weight">الوزن</option>
                                        <option value="volume">الحجم</option>
                                        <option value="temperature">درجة الحرارة</option>
                                        <option value="area">المساحة</option>
                                        <option value="speed">السرعة</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">القيمة</label>
                                    <input type="number" class="form-control" id="unitValue" placeholder="أدخل القيمة" step="any" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">من</label>
                                    <select class="form-select" id="fromUnit" required disabled>
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">إلى</label>
                                    <select class="form-select" id="toUnit" required disabled>
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>تحويل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="swapUnits()">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div id="unitConverterResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Setup form submission
    document.getElementById('unitConverterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performUnitConversion();
    });

    // Setup category change
    document.getElementById('unitCategory').addEventListener('change', function() {
        updateUnitOptions(this.value);
    });
}

// Update unit options based on category
function updateUnitOptions(category) {
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');

    // Clear existing options
    fromUnit.innerHTML = '<option value="">اختر الوحدة</option>';
    toUnit.innerHTML = '<option value="">اختر الوحدة</option>';

    if (!category || !unitConversions[category]) {
        fromUnit.disabled = true;
        toUnit.disabled = true;
        return;
    }

    const units = unitConversions[category].units;

    // Add options
    Object.keys(units).forEach(unitKey => {
        const unit = units[unitKey];
        const option = `<option value="${unitKey}">${unit.name} (${unit.symbol})</option>`;
        fromUnit.innerHTML += option;
        toUnit.innerHTML += option;
    });

    fromUnit.disabled = false;
    toUnit.disabled = false;
}

// Perform unit conversion
function performUnitConversion() {
    const category = document.getElementById('unitCategory').value;
    const value = parseFloat(document.getElementById('unitValue').value);
    const fromUnit = document.getElementById('fromUnit').value;
    const toUnit = document.getElementById('toUnit').value;
    const resultDiv = document.getElementById('unitConverterResult');

    // Validate input
    const validation = validateConversionInput(value, fromUnit, toUnit, category);
    if (!validation.valid) {
        showResult(validation.error, 'error', resultDiv);
        return;
    }

    if (fromUnit === toUnit) {
        const unitName = getUnitDisplayName(fromUnit, category);
        showResult(`${value} ${unitName} = ${value} ${unitName}`, 'success', resultDiv);
        return;
    }

    try {
        const result = convertUnits(value, fromUnit, toUnit, category);
        const formattedResult = formatResult(result);
        const fromUnitName = getUnitDisplayName(fromUnit, category);
        const toUnitName = getUnitDisplayName(toUnit, category);

        const resultHTML = `
            <div class="text-center">
                <h4 class="text-primary">${value} ${fromUnitName} = ${formattedResult} ${toUnitName}</h4>
                <p class="text-muted">فئة التحويل: ${unitConversions[category].name}</p>
            </div>
        `;

        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Unit conversion error:', error);
        showResult('حدث خطأ أثناء التحويل: ' + error.message, 'error', resultDiv);
    }
}

// Swap units
function swapUnits() {
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');

    const temp = fromUnit.value;
    fromUnit.value = toUnit.value;
    toUnit.value = temp;
}

// Age Calculator Tool
function loadAgeCalculator() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-birthday-cake me-2"></i>حساب العمر</h2>
                    <p class="text-muted">احسب عمرك بالسنوات والشهور والأيام بدقة</p>
                </div>

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="ageCalculatorForm">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">اليوم</label>
                                    <select class="form-select" id="birthDay" required>
                                        <option value="">اختر اليوم</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">الشهر</label>
                                    <select class="form-select" id="birthMonth" required>
                                        <option value="">اختر الشهر</option>
                                        <option value="1">يناير</option>
                                        <option value="2">فبراير</option>
                                        <option value="3">مارس</option>
                                        <option value="4">أبريل</option>
                                        <option value="5">مايو</option>
                                        <option value="6">يونيو</option>
                                        <option value="7">يوليو</option>
                                        <option value="8">أغسطس</option>
                                        <option value="9">سبتمبر</option>
                                        <option value="10">أكتوبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="12">ديسمبر</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label fw-bold">السنة</label>
                                    <select class="form-select" id="birthYear" required>
                                        <option value="">اختر السنة</option>
                                    </select>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>احسب العمر
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div id="ageCalculatorResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Populate day options
    const daySelect = document.getElementById('birthDay');
    for (let i = 1; i <= 31; i++) {
        daySelect.innerHTML += `<option value="${i}">${i}</option>`;
    }

    // Populate year options
    const yearSelect = document.getElementById('birthYear');
    const currentYear = new Date().getFullYear();
    for (let i = currentYear; i >= 1900; i--) {
        yearSelect.innerHTML += `<option value="${i}">${i}</option>`;
    }

    // Setup form submission
    document.getElementById('ageCalculatorForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performAgeCalculation();
    });
}

// Perform Age Calculation
function performAgeCalculation() {
    const day = parseInt(document.getElementById('birthDay').value);
    const month = parseInt(document.getElementById('birthMonth').value);
    const year = parseInt(document.getElementById('birthYear').value);
    const resultDiv = document.getElementById('ageCalculatorResult');

    if (!day || !month || !year) {
        showResult('يرجى إدخال تاريخ الميلاد كاملاً', 'error', resultDiv);
        return;
    }

    try {
        const birthDate = new Date(year, month - 1, day);
        const ageData = calculateAge(birthDate);

        const resultHTML = `
            <div class="text-center">
                <h4 class="text-primary mb-3">عمرك هو:</h4>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${ageData.years}</h5>
                            <small>سنة</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${ageData.months}</h5>
                            <small>شهر</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${ageData.days}</h5>
                            <small>يوم</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأشهر:</strong> ${ageData.totalMonths}</p>
                    </div>
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأسابيع:</strong> ${ageData.totalWeeks.toLocaleString('ar-SA')}</p>
                    </div>
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأيام:</strong> ${ageData.totalDays.toLocaleString('ar-SA')}</p>
                    </div>
                </div>
                <p class="text-muted mt-3">تاريخ الميلاد: ${formatArabicDate(ageData.birthDate)}</p>
            </div>
        `;

        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Age calculation error:', error);
        showResult('حدث خطأ أثناء حساب العمر: ' + error.message, 'error', resultDiv);
    }
}

// Date Difference Tool
function loadDateDifference() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-calendar-alt me-2"></i>الفرق بين التواريخ</h2>
                    <p class="text-muted">احسب الفرق بين تاريخين بالسنوات والشهور والأيام</p>
                </div>

                <div class="row">
                    <div class="col-lg-10 mx-auto">
                        <form id="dateDifferenceForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <h5 class="text-primary">التاريخ الأول</h5>
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <label class="form-label">اليوم</label>
                                            <select class="form-select" id="startDay" required>
                                                <option value="">يوم</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">الشهر</label>
                                            <select class="form-select" id="startMonth" required>
                                                <option value="">شهر</option>
                                                <option value="1">يناير</option>
                                                <option value="2">فبراير</option>
                                                <option value="3">مارس</option>
                                                <option value="4">أبريل</option>
                                                <option value="5">مايو</option>
                                                <option value="6">يونيو</option>
                                                <option value="7">يوليو</option>
                                                <option value="8">أغسطس</option>
                                                <option value="9">سبتمبر</option>
                                                <option value="10">أكتوبر</option>
                                                <option value="11">نوفمبر</option>
                                                <option value="12">ديسمبر</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">السنة</label>
                                            <select class="form-select" id="startYear" required>
                                                <option value="">سنة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-primary">التاريخ الثاني</h5>
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <label class="form-label">اليوم</label>
                                            <select class="form-select" id="endDay" required>
                                                <option value="">يوم</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">الشهر</label>
                                            <select class="form-select" id="endMonth" required>
                                                <option value="">شهر</option>
                                                <option value="1">يناير</option>
                                                <option value="2">فبراير</option>
                                                <option value="3">مارس</option>
                                                <option value="4">أبريل</option>
                                                <option value="5">مايو</option>
                                                <option value="6">يونيو</option>
                                                <option value="7">يوليو</option>
                                                <option value="8">أغسطس</option>
                                                <option value="9">سبتمبر</option>
                                                <option value="10">أكتوبر</option>
                                                <option value="11">نوفمبر</option>
                                                <option value="12">ديسمبر</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <label class="form-label">السنة</label>
                                            <select class="form-select" id="endYear" required>
                                                <option value="">سنة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>احسب الفرق
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div id="dateDifferenceResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Populate day options
    ['startDay', 'endDay'].forEach(id => {
        const daySelect = document.getElementById(id);
        for (let i = 1; i <= 31; i++) {
            daySelect.innerHTML += `<option value="${i}">${i}</option>`;
        }
    });

    // Populate year options
    ['startYear', 'endYear'].forEach(id => {
        const yearSelect = document.getElementById(id);
        const currentYear = new Date().getFullYear();
        for (let i = currentYear + 10; i >= 1900; i--) {
            yearSelect.innerHTML += `<option value="${i}">${i}</option>`;
        }
    });

    // Setup form submission
    document.getElementById('dateDifferenceForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performDateDifferenceCalculation();
    });
}

// Perform Date Difference Calculation
function performDateDifferenceCalculation() {
    const startDay = parseInt(document.getElementById('startDay').value);
    const startMonth = parseInt(document.getElementById('startMonth').value);
    const startYear = parseInt(document.getElementById('startYear').value);
    const endDay = parseInt(document.getElementById('endDay').value);
    const endMonth = parseInt(document.getElementById('endMonth').value);
    const endYear = parseInt(document.getElementById('endYear').value);
    const resultDiv = document.getElementById('dateDifferenceResult');

    if (!startDay || !startMonth || !startYear || !endDay || !endMonth || !endYear) {
        showResult('يرجى إدخال التاريخين كاملين', 'error', resultDiv);
        return;
    }

    try {
        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);
        const diffData = calculateDateDifference(startDate, endDate);

        const resultHTML = `
            <div class="text-center">
                <h4 class="text-primary mb-3">الفرق بين التاريخين:</h4>
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${diffData.years}</h5>
                            <small>سنة</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${diffData.months}</h5>
                            <small>شهر</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light p-3 rounded">
                            <h5 class="text-primary">${diffData.days}</h5>
                            <small>يوم</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row g-3 mt-2">
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأيام:</strong> ${diffData.totalDays.toLocaleString('ar-SA')}</p>
                    </div>
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأسابيع:</strong> ${diffData.totalWeeks.toLocaleString('ar-SA')}</p>
                    </div>
                    <div class="col-md-4">
                        <p class="mb-1"><strong>إجمالي الأشهر:</strong> ${diffData.totalMonths}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-muted">من: ${formatArabicDate(diffData.startDate)}</p>
                    <p class="text-muted">إلى: ${formatArabicDate(diffData.endDate)}</p>
                </div>
            </div>
        `;

        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Date difference calculation error:', error);
        showResult('حدث خطأ أثناء حساب الفرق بين التاريخين', 'error', resultDiv);
    }
}

// Word Counter Tool
function loadWordCounter() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-file-word me-2"></i>عد الكلمات والأحرف</h2>
                    <p class="text-muted">عد الكلمات والأحرف والفقرات في النصوص العربية والإنجليزية</p>
                </div>

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="mb-3">
                            <label class="form-label fw-bold">النص المراد تحليله</label>
                            <textarea class="form-control" id="textToAnalyze" rows="8" placeholder="اكتب أو الصق النص هنا..."></textarea>
                            <div class="form-text">يمكنك كتابة أو لصق النص باللغة العربية أو الإنجليزية</div>
                        </div>

                        <div class="text-center mb-4">
                            <button type="button" class="btn btn-primary" onclick="analyzeText()">
                                <i class="fas fa-search me-2"></i>تحليل النص
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearText()">
                                <i class="fas fa-trash me-2"></i>مسح النص
                            </button>
                        </div>

                        <div id="wordCountResult" class="result-display">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="wordCount">0</h4>
                                        <small>كلمة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="charCount">0</h4>
                                        <small>حرف</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="charCountNoSpaces">0</h4>
                                        <small>حرف بدون مسافات</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="sentenceCount">0</h4>
                                        <small>جملة</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mt-3">
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="paragraphCount">0</h4>
                                        <small>فقرة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="lineCount">0</h4>
                                        <small>سطر</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="readingTime">0</h4>
                                        <small>دقيقة قراءة</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary mb-1" id="speakingTime">0</h4>
                                        <small>دقيقة إلقاء</small>
                                    </div>
                                </div>
                            </div>

                            <div id="detailedStats" class="mt-4 d-none">
                                <h5 class="text-primary">إحصائيات مفصلة</h5>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <p class="mb-1"><strong>كلمات عربية:</strong> <span id="arabicWords">0</span></p>
                                        <p class="mb-1"><strong>كلمات إنجليزية:</strong> <span id="englishWords">0</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1"><strong>أرقام:</strong> <span id="numberCount">0</span></p>
                                        <p class="mb-1"><strong>علامات ترقيم:</strong> <span id="punctuationCount">0</span></p>
                                    </div>
                                    <div class="col-md-4">
                                        <p class="mb-1"><strong>متوسط الكلمات/الجملة:</strong> <span id="avgWordsPerSentence">0</span></p>
                                        <p class="mb-1"><strong>متوسط الأحرف/الكلمة:</strong> <span id="avgCharsPerWord">0</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Setup real-time analysis
    const textArea = document.getElementById('textToAnalyze');
    textArea.addEventListener('input', function() {
        analyzeText();
    });

    // Initial analysis
    analyzeText();
}

// Analyze Text Function
function analyzeText() {
    const text = document.getElementById('textToAnalyze').value;
    const stats = countWords(text);

    // Update basic stats
    document.getElementById('wordCount').textContent = stats.words;
    document.getElementById('charCount').textContent = stats.characters;
    document.getElementById('charCountNoSpaces').textContent = stats.charactersNoSpaces;
    document.getElementById('sentenceCount').textContent = stats.sentences;
    document.getElementById('paragraphCount').textContent = stats.paragraphs;
    document.getElementById('lineCount').textContent = stats.lines;
    document.getElementById('readingTime').textContent = stats.readingTime;
    document.getElementById('speakingTime').textContent = stats.speakingTime;

    // Update detailed stats
    document.getElementById('arabicWords').textContent = stats.arabicWords;
    document.getElementById('englishWords').textContent = stats.englishWords;
    document.getElementById('numberCount').textContent = stats.numbers;
    document.getElementById('punctuationCount').textContent = stats.punctuation;
    document.getElementById('avgWordsPerSentence').textContent = stats.averageWordsPerSentence;
    document.getElementById('avgCharsPerWord').textContent = stats.averageCharactersPerWord;

    // Show detailed stats if there's text
    const detailedStats = document.getElementById('detailedStats');
    if (text.trim().length > 0) {
        detailedStats.classList.remove('d-none');
    } else {
        detailedStats.classList.add('d-none');
    }
}

// Clear Text Function
function clearText() {
    document.getElementById('textToAnalyze').value = '';
    analyzeText();
}

// Text on Image Tool
function loadTextOnImage() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-image me-2"></i>الكتابة على الصور</h2>
                    <p class="text-muted">أضف نصوص مخصصة على الصور مع خيارات تنسيق متنوعة</p>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="mb-3">
                            <label class="form-label fw-bold">اختر الصورة</label>
                            <input type="file" class="form-control" id="imageFile" accept="image/*">
                        </div>

                        <div class="text-center">
                            <canvas id="imageCanvas" class="border rounded shadow-sm" style="max-width: 100%;"></canvas>
                        </div>

                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-success" onclick="downloadImage()">
                                <i class="fas fa-download me-2"></i>تحميل الصورة
                            </button>
                            <button type="button" class="btn btn-outline-danger ms-2" onclick="clearCanvas()">
                                <i class="fas fa-trash me-2"></i>مسح الكل
                            </button>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">إعدادات النص</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">النص</label>
                                    <textarea class="form-control" id="textInput" rows="3" placeholder="اكتب النص هنا..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">حجم الخط</label>
                                    <input type="range" class="form-range" id="fontSize" min="12" max="72" value="24">
                                    <small class="text-muted">الحجم: <span id="fontSizeValue">24</span>px</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">لون النص</label>
                                    <input type="color" class="form-control form-control-color" id="textColor" value="#000000">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">لون الخلفية</label>
                                    <input type="color" class="form-control form-control-color" id="backgroundColor" value="#ffffff">
                                    <div class="form-check mt-2">
                                        <input class="form-check-input" type="checkbox" id="transparentBg" checked>
                                        <label class="form-check-label" for="transparentBg">خلفية شفافة</label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">محاذاة النص</label>
                                    <select class="form-select" id="textAlign">
                                        <option value="center">وسط</option>
                                        <option value="right">يمين</option>
                                        <option value="left">يسار</option>
                                    </select>
                                </div>

                                <div class="text-center">
                                    <button type="button" class="btn btn-primary" onclick="addTextToImage()">
                                        <i class="fas fa-plus me-2"></i>إضافة النص
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Initialize text on image
    initTextOnImage();

    // Setup event listeners
    document.getElementById('imageFile').addEventListener('change', handleImageUpload);
    document.getElementById('fontSize').addEventListener('input', function() {
        document.getElementById('fontSizeValue').textContent = this.value;
    });
}

// Handle Image Upload
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (file) {
        loadImageOnCanvas(file).then(() => {
            console.log('Image loaded successfully');
        }).catch(error => {
            alert('خطأ في تحميل الصورة: ' + error.message);
        });
    }
}

// Add Text to Image
function addTextToImage() {
    const text = document.getElementById('textInput').value.trim();
    if (!text) {
        alert('يرجى إدخال النص أولاً');
        return;
    }

    const options = {
        fontSize: parseInt(document.getElementById('fontSize').value),
        color: document.getElementById('textColor').value,
        backgroundColor: document.getElementById('transparentBg').checked ? 'transparent' : document.getElementById('backgroundColor').value,
        textAlign: document.getElementById('textAlign').value
    };

    addTextElement(text, options);

    // Clear text input
    document.getElementById('textInput').value = '';
}

// Download Image
function downloadImage() {
    if (!canvas) {
        alert('لا توجد صورة للتحميل');
        return;
    }

    downloadCanvasImage('image-with-text.png');
}

// Clear Canvas
function clearCanvas() {
    if (confirm('هل أنت متأكد من مسح جميع النصوص؟')) {
        textElements = [];
        selectedElement = null;
        redrawCanvas();
    }
}

// Tic Tac Toe Game
function loadTicTacToe() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-gamepad me-2"></i>لعبة إكس أوه</h2>
                    <p class="text-muted">العب لعبة إكس أوه مع الأصدقاء محلياً أو ضد الكمبيوتر</p>
                </div>

                <div class="row">
                    <div class="col-lg-6 mx-auto">
                        <div class="text-center mb-4">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="setGameMode('local')">لاعبان</button>
                                <button type="button" class="btn btn-outline-primary active" onclick="setGameMode('ai')">ضد الكمبيوتر</button>
                            </div>
                        </div>

                        <div id="gameStatus" class="alert alert-info text-center">
                            <span id="currentPlayer">دور: اللاعب الأول (X)</span>
                        </div>

                        <div class="tic-tac-toe-board mx-auto mb-4">
                            <div class="tic-tac-toe-grid">
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(0)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(1)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(2)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(3)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(4)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(5)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(6)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(7)"></div>
                                <div class="tic-tac-toe-cell" onclick="handleCellClick(8)"></div>
                            </div>
                        </div>

                        <div class="text-center mb-4">
                            <button type="button" class="btn btn-primary" onclick="resetGame()">
                                <i class="fas fa-redo me-2"></i>لعبة جديدة
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetScores()">
                                <i class="fas fa-trash me-2"></i>مسح النتائج
                            </button>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0 text-center">النتائج</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h4 class="text-primary" id="scoreX">0</h4>
                                        <small>اللاعب الأول</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-warning" id="scoreDraws">0</h4>
                                        <small>تعادل</small>
                                    </div>
                                    <div class="col-4">
                                        <h4 class="text-success" id="scoreO">0</h4>
                                        <small>اللاعب الثاني</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Initialize game
    initTicTacToe();
}

// Handle Cell Click
function handleCellClick(cellIndex) {
    makeMove(cellIndex);
}

// Setup Scroll Animations
function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe tool cards
    document.querySelectorAll('.tool-card').forEach(card => {
        observer.observe(card);
    });

    // Observe other elements
    document.querySelectorAll('.tool-content, .result-display').forEach(element => {
        observer.observe(element);
    });
}

// Setup Tool Card Animations
function setupToolCardAnimations() {
    const toolCards = document.querySelectorAll('.tool-card');

    toolCards.forEach((card, index) => {
        // Add stagger delay
        card.style.animationDelay = `${index * 0.1}s`;

        // Add hover sound effect (optional)
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Enhanced Loading Function
function showLoading(container) {
    if (!container) return;

    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'loading-overlay';
    loadingOverlay.innerHTML = `
        <div class="loading-spinner"></div>
    `;

    container.style.position = 'relative';
    container.appendChild(loadingOverlay);

    return loadingOverlay;
}

// Hide Loading Function
function hideLoading(container) {
    if (!container) return;

    const loadingOverlay = container.querySelector('.loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

// Enhanced Show Result Function
function showResultEnhanced(content, type = 'info', container = null, animate = true) {
    if (!container) return;

    // Hide loading if present
    hideLoading(container);

    container.innerHTML = content;
    container.className = `result-display ${type}`;
    container.classList.remove('d-none');

    // Add animation
    if (animate) {
        container.classList.add('fade-in');

        // Add stagger animation to child elements
        const children = container.querySelectorAll('.bg-light, .alert, .row > div');
        children.forEach((child, index) => {
            child.style.animationDelay = `${index * 0.1}s`;
            child.classList.add('slide-in-up');
        });
    }
}

// Back to Home Function
function backToHome() {
    const toolContent = document.getElementById('tool-content');
    toolContent.classList.add('d-none');
    scrollToTop();
    currentTool = null;
}
