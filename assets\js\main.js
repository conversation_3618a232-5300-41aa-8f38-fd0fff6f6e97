// Main JavaScript file for Arabic Tools Website

// Global variables
let currentTool = null;
const API_KEY = 'your-exchange-api-key'; // Replace with actual API key

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize Application
function initializeApp() {
    setupEventListeners();
    setupSmoothScrolling();
    setupNavbarScroll();
}

// Setup Event Listeners
function setupEventListeners() {
    // Tool card clicks
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const toolName = this.dataset.tool;
            loadTool(toolName);
        });
    });

    // Navigation links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                if (targetId === 'home') {
                    scrollToTop();
                } else {
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    });
}

// Setup Smooth Scrolling
function setupSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Setup Navbar Scroll Effect
function setupNavbarScroll() {
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.backgroundColor = 'rgba(37, 99, 235, 0.95)';
        } else {
            navbar.style.backgroundColor = 'rgba(37, 99, 235, 1)';
        }
    });
}

// Load Tool Function
function loadTool(toolName) {
    const toolContent = document.getElementById('tool-content');
    toolContent.innerHTML = '';
    toolContent.classList.remove('d-none');
    
    // Scroll to tool content
    toolContent.scrollIntoView({ behavior: 'smooth' });
    
    // Load specific tool
    switch(toolName) {
        case 'currency-converter':
            loadCurrencyConverter();
            break;
        case 'number-to-words':
            loadNumberToWords();
            break;
        case 'unit-converter':
            loadUnitConverter();
            break;
        case 'age-calculator':
            loadAgeCalculator();
            break;
        case 'date-difference':
            loadDateDifference();
            break;
        case 'word-counter':
            loadWordCounter();
            break;
        case 'text-on-image':
            loadTextOnImage();
            break;
        case 'tic-tac-toe':
            loadTicTacToe();
            break;
        default:
            showError('الأداة غير متوفرة حالياً');
    }
    
    currentTool = toolName;
}

// Currency Converter Tool
function loadCurrencyConverter() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-exchange-alt me-2"></i>تحويل العملات</h2>
                    <p class="text-muted">تحويل بين العملات المختلفة بأسعار حقيقية ومحدثة</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="currencyForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">المبلغ</label>
                                    <input type="number" class="form-control" id="amount" placeholder="أدخل المبلغ" min="0" step="0.01" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">من</label>
                                    <select class="form-select" id="fromCurrency" required>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="GBP">جنيه إسترليني (GBP)</option>
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="JOD">دينار أردني (JOD)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">إلى</label>
                                    <select class="form-select" id="toCurrency" required>
                                        <option value="SAR">ريال سعودي (SAR)</option>
                                        <option value="USD">دولار أمريكي (USD)</option>
                                        <option value="EUR">يورو (EUR)</option>
                                        <option value="GBP">جنيه إسترليني (GBP)</option>
                                        <option value="AED">درهم إماراتي (AED)</option>
                                        <option value="EGP">جنيه مصري (EGP)</option>
                                        <option value="JOD">دينار أردني (JOD)</option>
                                        <option value="KWD">دينار كويتي (KWD)</option>
                                    </select>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>تحويل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="swapCurrencies()">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                        
                        <div id="currencyResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('tool-content').innerHTML = html;
    
    // Setup form submission
    document.getElementById('currencyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        convertCurrency();
    });
}

// Convert Currency Function
async function convertCurrency() {
    const amount = document.getElementById('amount').value;
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    const resultDiv = document.getElementById('currencyResult');
    
    if (!amount || amount <= 0) {
        showResult('يرجى إدخال مبلغ صحيح', 'error', resultDiv);
        return;
    }
    
    if (fromCurrency === toCurrency) {
        showResult(`${amount} ${fromCurrency} = ${amount} ${toCurrency}`, 'success', resultDiv);
        return;
    }
    
    // Show loading
    showResult('<div class="loading"></div> جاري التحويل...', 'info', resultDiv);
    
    try {
        // Using a free API for currency conversion
        const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${fromCurrency}`);
        const data = await response.json();
        
        if (data.rates && data.rates[toCurrency]) {
            const rate = data.rates[toCurrency];
            const convertedAmount = (amount * rate).toFixed(2);
            
            const resultHTML = `
                <div class="text-center">
                    <h4 class="text-primary">${amount} ${fromCurrency} = ${convertedAmount} ${toCurrency}</h4>
                    <p class="text-muted">سعر الصرف: 1 ${fromCurrency} = ${rate.toFixed(4)} ${toCurrency}</p>
                    <small class="text-muted">آخر تحديث: ${new Date().toLocaleString('ar-SA')}</small>
                </div>
            `;
            
            showResult(resultHTML, 'success', resultDiv);
        } else {
            showResult('عذراً، لا يمكن الحصول على سعر الصرف حالياً', 'error', resultDiv);
        }
    } catch (error) {
        console.error('Currency conversion error:', error);
        showResult('حدث خطأ أثناء التحويل. يرجى المحاولة مرة أخرى', 'error', resultDiv);
    }
}

// Swap Currencies Function
function swapCurrencies() {
    const fromCurrency = document.getElementById('fromCurrency');
    const toCurrency = document.getElementById('toCurrency');
    
    const temp = fromCurrency.value;
    fromCurrency.value = toCurrency.value;
    toCurrency.value = temp;
}

// Number to Words Tool
function loadNumberToWords() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-spell-check me-2"></i>تفقيط الأرقام</h2>
                    <p class="text-muted">تحويل الأرقام إلى كلمات باللغة العربية بطريقة محاسبية صحيحة</p>
                </div>
                
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="numberToWordsForm">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الرقم المراد تفقيطه</label>
                                <input type="number" class="form-control" id="numberInput" placeholder="أدخل الرقم (مثال: 1234.56)" step="0.01" required>
                                <div class="form-text">يمكن إدخال الأرقام العشرية أيضاً</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label fw-bold">العملة</label>
                                <select class="form-select" id="currencyType">
                                    <option value="SAR">ريال سعودي</option>
                                    <option value="USD">دولار أمريكي</option>
                                    <option value="EUR">يورو</option>
                                    <option value="EGP">جنيه مصري</option>
                                    <option value="AED">درهم إماراتي</option>
                                    <option value="JOD">دينار أردني</option>
                                    <option value="KWD">دينار كويتي</option>
                                    <option value="NONE">بدون عملة</option>
                                </select>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic me-2"></i>تفقيط الرقم
                                </button>
                            </div>
                        </form>
                        
                        <div id="numberToWordsResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('tool-content').innerHTML = html;
    
    // Setup form submission
    document.getElementById('numberToWordsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        convertNumberToWords();
    });
}

// Convert Number to Words Function
function convertNumberToWords() {
    const number = parseFloat(document.getElementById('numberInput').value);
    const currency = document.getElementById('currencyType').value;
    const resultDiv = document.getElementById('numberToWordsResult');
    
    if (isNaN(number)) {
        showResult('يرجى إدخال رقم صحيح', 'error', resultDiv);
        return;
    }
    
    if (number < 0) {
        showResult('لا يمكن تفقيط الأرقام السالبة', 'error', resultDiv);
        return;
    }
    
    if (number > 999999999999) {
        showResult('الرقم كبير جداً. الحد الأقصى هو 999,999,999,999', 'error', resultDiv);
        return;
    }
    
    try {
        const words = numberToArabicWords(number, currency);
        const resultHTML = `
            <div class="text-center">
                <h5 class="text-primary mb-3">النتيجة:</h5>
                <div class="alert alert-success">
                    <h4>${words}</h4>
                </div>
                <p class="text-muted">الرقم: ${number.toLocaleString('ar-SA')}</p>
            </div>
        `;
        
        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Number to words error:', error);
        showResult('حدث خطأ أثناء التفقيط', 'error', resultDiv);
    }
}

// Utility Functions
function showResult(content, type = 'info', container = null) {
    if (!container) return;
    
    container.innerHTML = content;
    container.className = `result-display ${type}`;
    container.classList.remove('d-none');
    
    // Add animation
    container.classList.add('fade-in');
}

function showError(message) {
    const toolContent = document.getElementById('tool-content');
    toolContent.innerHTML = `
        <div class="container">
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        </div>
    `;
}

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Unit Converter Tool
function loadUnitConverter() {
    const html = `
        <div class="container">
            <div class="tool-content fade-in">
                <div class="tool-header">
                    <h2><i class="fas fa-ruler me-2"></i>تحويل وحدات القياس</h2>
                    <p class="text-muted">تحويل بين وحدات الطول والوزن والحجم ودرجة الحرارة</p>
                </div>

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <form id="unitConverterForm">
                            <div class="row g-3">
                                <div class="col-md-12">
                                    <label class="form-label fw-bold">نوع الوحدة</label>
                                    <select class="form-select" id="unitCategory" required>
                                        <option value="">اختر نوع الوحدة</option>
                                        <option value="length">الطول</option>
                                        <option value="weight">الوزن</option>
                                        <option value="volume">الحجم</option>
                                        <option value="temperature">درجة الحرارة</option>
                                        <option value="area">المساحة</option>
                                        <option value="speed">السرعة</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">القيمة</label>
                                    <input type="number" class="form-control" id="unitValue" placeholder="أدخل القيمة" step="any" required>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">من</label>
                                    <select class="form-select" id="fromUnit" required disabled>
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label fw-bold">إلى</label>
                                    <select class="form-select" id="toUnit" required disabled>
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-calculator me-2"></i>تحويل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="swapUnits()">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </form>

                        <div id="unitConverterResult" class="result-display d-none mt-4">
                            <!-- Result will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('tool-content').innerHTML = html;

    // Setup form submission
    document.getElementById('unitConverterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        performUnitConversion();
    });

    // Setup category change
    document.getElementById('unitCategory').addEventListener('change', function() {
        updateUnitOptions(this.value);
    });
}

// Update unit options based on category
function updateUnitOptions(category) {
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');

    // Clear existing options
    fromUnit.innerHTML = '<option value="">اختر الوحدة</option>';
    toUnit.innerHTML = '<option value="">اختر الوحدة</option>';

    if (!category || !unitConversions[category]) {
        fromUnit.disabled = true;
        toUnit.disabled = true;
        return;
    }

    const units = unitConversions[category].units;

    // Add options
    Object.keys(units).forEach(unitKey => {
        const unit = units[unitKey];
        const option = `<option value="${unitKey}">${unit.name} (${unit.symbol})</option>`;
        fromUnit.innerHTML += option;
        toUnit.innerHTML += option;
    });

    fromUnit.disabled = false;
    toUnit.disabled = false;
}

// Perform unit conversion
function performUnitConversion() {
    const category = document.getElementById('unitCategory').value;
    const value = parseFloat(document.getElementById('unitValue').value);
    const fromUnit = document.getElementById('fromUnit').value;
    const toUnit = document.getElementById('toUnit').value;
    const resultDiv = document.getElementById('unitConverterResult');

    // Validate input
    const validation = validateConversionInput(value, fromUnit, toUnit, category);
    if (!validation.valid) {
        showResult(validation.error, 'error', resultDiv);
        return;
    }

    if (fromUnit === toUnit) {
        const unitName = getUnitDisplayName(fromUnit, category);
        showResult(`${value} ${unitName} = ${value} ${unitName}`, 'success', resultDiv);
        return;
    }

    try {
        const result = convertUnits(value, fromUnit, toUnit, category);
        const formattedResult = formatResult(result);
        const fromUnitName = getUnitDisplayName(fromUnit, category);
        const toUnitName = getUnitDisplayName(toUnit, category);

        const resultHTML = `
            <div class="text-center">
                <h4 class="text-primary">${value} ${fromUnitName} = ${formattedResult} ${toUnitName}</h4>
                <p class="text-muted">فئة التحويل: ${unitConversions[category].name}</p>
            </div>
        `;

        showResult(resultHTML, 'success', resultDiv);
    } catch (error) {
        console.error('Unit conversion error:', error);
        showResult('حدث خطأ أثناء التحويل: ' + error.message, 'error', resultDiv);
    }
}

// Swap units
function swapUnits() {
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');

    const temp = fromUnit.value;
    fromUnit.value = toUnit.value;
    toUnit.value = temp;
}

// Back to Home Function
function backToHome() {
    const toolContent = document.getElementById('tool-content');
    toolContent.classList.add('d-none');
    scrollToTop();
    currentTool = null;
}
